import 'package:crud/controllers/counter_controller.dart';
import 'package:crud/controllers/initial_controller.dart';
// import 'package:flutter/material.dart';
import 'package:refreshed/refreshed.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

class HomeView extends StatelessWidget {
  HomeView({super.key});

  final controller = Get.put(CounterController());
  final initialController = Get.put(InitialController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(() => Center(child: Text('Hello World! - ${controller.count}'))),
          PrimaryButton(
            onPressed: () => controller.increment(),
            trailing: const Icon(Icons.add),
            child: const Text('Add'),
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 10),
          Primary<PERSON>utton(
            onPressed: () => controller.,
            trailing: const Icon(Icons.remove),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
