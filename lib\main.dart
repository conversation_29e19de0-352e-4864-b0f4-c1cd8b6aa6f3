import 'package:refreshed/refreshed.dart';
import 'package:crud/controllers/initial_controller.dart';
import 'package:crud/views/home_view.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

void main() {
  final initialController = Get.put(InitialController());
  runApp(
    ShadcnApp(
      title: 'My App',
      home: MainApp(),
      theme: ThemeData(
        colorScheme: initialController.changeTheme(
          initialController.currentTheme.value,
        ),
        radius: 0.5,
      ),
    ),
  );
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return HomeView();
  }
}
